import 'dart:convert';
import 'dart:io';
import 'package:dartssh2/dartssh2.dart';
import '../models/hotspot_profile.dart';

class SSHService {
  SSHClient? _client;
  String? _host;
  int? _port;
  String? _username;
  String? _password;

  Future<bool> connect({
    required String host,
    required int port,
    required String username,
    required String password,
  }) async {
    try {
      _host = host;
      _port = port;
      _username = username;
      _password = password;

      final socket = await SSHSocket.connect(_host!, _port!);
      _client = SSHClient(
        socket,
        username: _username!,
        onPasswordRequest: () => _password!,
      );

      return true;
    } catch (e) {
      print('SSH Connection Error: $e');
      return false;
    }
  }

  Future<HotspotProfile?> getMostUsedProfile() async {
    if (_client == null) return null;

    try {
      // Get all hotspot profiles
      final result = await _client!.run('/ip hotspot user-profile print');
      final output = utf8.decode(result);
      
      final profiles = <HotspotProfile>[];
      final profileBlocks = output.split('\n\n');
      
      for (String block in profileBlocks) {
        if (block.trim().isNotEmpty && block.contains('name=')) {
          try {
            final profile = HotspotProfile.fromString(block);
            if (profile.name.isNotEmpty && profile.htmlDirectory.isNotEmpty) {
              profiles.add(profile);
            }
          } catch (e) {
            print('Error parsing profile: $e');
          }
        }
      }

      // Find profile with most users
      if (profiles.isNotEmpty) {
        profiles.sort((a, b) => b.userCount.compareTo(a.userCount));
        return profiles.first;
      }

      return null;
    } catch (e) {
      print('Error getting profiles: $e');
      return null;
    }
  }

  Future<List<String>> listHtmlFiles(String directory) async {
    if (_client == null) return [];

    try {
      final result = await _client!.run('find $directory -name "*.html" -type f');
      final output = utf8.decode(result);
      
      return output
          .split('\n')
          .where((line) => line.trim().isNotEmpty)
          .toList();
    } catch (e) {
      print('Error listing HTML files: $e');
      return [];
    }
  }

  Future<String?> readFile(String filePath) async {
    if (_client == null) return null;

    try {
      final result = await _client!.run('cat "$filePath"');
      return utf8.decode(result);
    } catch (e) {
      print('Error reading file: $e');
      return null;
    }
  }

  Future<bool> writeFile(String filePath, String content) async {
    if (_client == null) return false;

    try {
      // Create a temporary file with the content
      final tempFile = '/tmp/temp_html_${DateTime.now().millisecondsSinceEpoch}.html';
      
      // Write content to temp file
      await _client!.run('cat > "$tempFile"', stdin: utf8.encode(content));
      
      // Move temp file to target location
      await _client!.run('mv "$tempFile" "$filePath"');
      
      return true;
    } catch (e) {
      print('Error writing file: $e');
      return false;
    }
  }

  Future<bool> uploadImage(String localImagePath, String remoteImagePath) async {
    if (_client == null) return false;

    try {
      final file = File(localImagePath);
      if (!await file.exists()) return false;

      final bytes = await file.readAsBytes();
      
      // Create temporary file on server
      final tempFile = '/tmp/temp_image_${DateTime.now().millisecondsSinceEpoch}';
      
      // Upload image data
      await _client!.run('cat > "$tempFile"', stdin: bytes);
      
      // Move to target location
      await _client!.run('mv "$tempFile" "$remoteImagePath"');
      
      return true;
    } catch (e) {
      print('Error uploading image: $e');
      return false;
    }
  }

  void disconnect() {
    _client?.close();
    _client = null;
  }
}
