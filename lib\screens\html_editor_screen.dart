import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../services/ssh_service.dart';
import '../services/html_parser_service.dart';
import '../models/hotspot_profile.dart';
import '../models/html_content.dart';
import 'web_preview_screen.dart';

class HtmlEditorScreen extends StatefulWidget {
  final SSHService sshService;
  final String filePath;
  final String htmlContent;
  final HotspotProfile profile;

  const HtmlEditorScreen({
    super.key,
    required this.sshService,
    required this.filePath,
    required this.htmlContent,
    required this.profile,
  });

  @override
  State<HtmlEditorScreen> createState() => _HtmlEditorScreenState();
}

class _HtmlEditorScreenState extends State<HtmlEditorScreen> {
  late HtmlContent _htmlContent;
  final List<TextEditingController> _controllers = [];
  bool _isSaving = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _parseHtmlContent();
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _parseHtmlContent() {
    _htmlContent = HtmlParserService.parseHtml(widget.htmlContent);

    // Create controllers for each editable line
    _controllers.clear();
    for (var line in _htmlContent.editableLines) {
      final controller = TextEditingController(text: line.currentText);
      controller.addListener(() {
        _onTextChanged(line, controller.text);
      });
      _controllers.add(controller);
    }
  }

  void _onTextChanged(EditableTextLine line, String newText) {
    setState(() {
      line.currentText = newText;
      _hasChanges = true;
    });
  }

  Future<void> _saveChanges() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final modifiedHtml = HtmlParserService.rebuildHtml(
        widget.htmlContent,
        _htmlContent.editableLines,
      );

      final success = await widget.sshService.writeFile(
        widget.filePath,
        modifiedHtml,
      );

      if (success) {
        setState(() {
          _hasChanges = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ التغييرات بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في حفظ التغييرات'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الحفظ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<void> _previewPage() async {
    if (_hasChanges) {
      final shouldSave = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('حفظ التغييرات'),
          content: const Text(
              'هناك تغييرات غير محفوظة. هل تريد حفظها قبل المعاينة؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('لا'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('نعم'),
            ),
          ],
        ),
      );

      if (shouldSave == true) {
        await _saveChanges();
      }
    }

    if (mounted) {
      final modifiedHtml = HtmlParserService.rebuildHtml(
        widget.htmlContent,
        _htmlContent.editableLines,
      );

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => WebPreviewScreen(
            htmlContent: modifiedHtml,
            title: widget.filePath.split('/').last,
          ),
        ),
      );
    }
  }

  Future<void> _replaceImage(String imagePath) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image != null) {
      try {
        // Extract filename from the original image path
        final fileName = imagePath.split('/').last;
        final remoteImagePath = '${widget.profile.htmlDirectory}/$fileName';

        final success = await widget.sshService.uploadImage(
          image.path,
          remoteImagePath,
        );

        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم استبدال الصورة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('فشل في استبدال الصورة'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في استبدال الصورة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.filePath.split('/').last),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.preview),
            onPressed: _previewPage,
            tooltip: 'معاينة',
          ),
          IconButton(
            icon: _isSaving
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Icon(
                    Icons.save,
                    color: _hasChanges ? Colors.green : null,
                  ),
            onPressed: _isSaving ? null : _saveChanges,
            tooltip: 'حفظ',
          ),
        ],
      ),
      body: Column(
        children: [
          if (_hasChanges)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              color: Colors.orange.shade100,
              child: const Text(
                'هناك تغييرات غير محفوظة',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Text editing section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'تحرير النصوص',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ..._buildTextEditors(),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Images section
                if (_htmlContent.imageUrls.isNotEmpty)
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'الصور',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._buildImageList(),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildTextEditors() {
    final widgets = <Widget>[];

    for (int i = 0; i < _htmlContent.editableLines.length; i++) {
      final line = _htmlContent.editableLines[i];
      final controller = _controllers[i];

      widgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'السطر ${line.lineNumber} (${line.htmlTag})',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              TextFormField(
                controller: controller,
                maxLines: null,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.all(12),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return widgets;
  }

  List<Widget> _buildImageList() {
    return _htmlContent.imageUrls.map((imagePath) {
      return Card(
        margin: const EdgeInsets.only(bottom: 8),
        child: ListTile(
          leading: const Icon(Icons.image, color: Colors.blue),
          title: Text(imagePath.split('/').last),
          subtitle: Text(
            imagePath,
            style: const TextStyle(fontSize: 12),
          ),
          trailing: ElevatedButton.icon(
            onPressed: () => _replaceImage(imagePath),
            icon: const Icon(Icons.photo_library, size: 16),
            label: const Text('استبدال'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
        ),
      );
    }).toList();
  }
}
