import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';

class WebPreviewScreen extends StatefulWidget {
  final String htmlContent;
  final String title;

  const WebPreviewScreen({
    super.key,
    required this.htmlContent,
    required this.title,
  });

  @override
  State<WebPreviewScreen> createState() => _WebPreviewScreenState();
}

class _WebPreviewScreenState extends State<WebPreviewScreen> {
  late final WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ في تحميل الصفحة: ${error.description}'),
                backgroundColor: Colors.red,
              ),
            );
          },
        ),
      );

    _loadHtmlContent();
  }

  void _loadHtmlContent() {
    // Convert HTML content to data URL
    final encodedHtml = base64Encode(utf8.encode(widget.htmlContent));
    final dataUrl = 'data:text/html;charset=utf-8;base64,$encodedHtml';
    
    _controller.loadRequest(Uri.parse(dataUrl));
  }

  void _refreshPage() {
    _loadHtmlContent();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('معاينة - ${widget.title}'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshPage,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل الصفحة...'),
                ],
              ),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _refreshPage,
        tooltip: 'تحديث الصفحة',
        child: const Icon(Icons.refresh),
      ),
    );
  }
}
