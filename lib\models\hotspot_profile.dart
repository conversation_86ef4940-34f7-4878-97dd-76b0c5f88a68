import 'dart:convert'; // تأكد من استيراد هذه المكتبة

// افترض أن لديك هذا الكلاس لتمثيل بيانات البروفايل
class HotspotProfile {
  final String name;
  final String htmlDirectory;

  HotspotProfile({required this.name, required this.htmlDirectory});

  factory HotspotProfile.fromString(String profileBlock) {
    String name = '';
    String htmlDirectory = '';

    final lines = profileBlock.split('\n');

    for (final line in lines) {
      final trimmedLine = line.trim();

      if (trimmedLine.contains('name=')) {
        final match = RegExp(r'name="([^"]+)"').firstMatch(trimmedLine);
        if (match != null && match.groupCount > 0) {
          name = match.group(1)!;
        }
      } else if (trimmedLine.contains('html-directory=')) {
        final match = RegExp(r'html-directory="([^"]+)"').firstMatch(trimmedLine);
        if (match != null && match.groupCount > 0) {
          htmlDirectory = match.group(1)!;
        }
      }
    }
    return HotspotProfile(name: name, htmlDirectory: htmlDirectory);
  }

  // أضف هذه الدالة للمساعدة في الطباعة
  @override
  String toString() {
    return 'HotspotProfile(name: $name, htmlDirectory: $htmlDirectory)';
  }
}

