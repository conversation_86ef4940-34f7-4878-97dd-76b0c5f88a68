class HotspotProfile {
  final String name;
  final String htmlDirectory;
  final int userCount;

  HotspotProfile({
    required this.name,
    required this.htmlDirectory,
    required this.userCount,
  });

  factory HotspotProfile.fromString(String profileData) {
    final lines = profileData.split('\n');
    String name = '';
    String htmlDirectory = '';
    int userCount = 0;

    for (String line in lines) {
      line = line.trim();
      if (line.startsWith('name=')) {
        name = line.substring(5);
      } else if (line.startsWith('html-directory=')) {
        htmlDirectory = line.substring(15);
      } else if (line.contains('users=')) {
        final userMatch = RegExp(r'users=(\d+)').firstMatch(line);
        if (userMatch != null) {
          userCount = int.tryParse(userMatch.group(1) ?? '0') ?? 0;
        }
      }
    }

    return HotspotProfile(
      name: name,
      htmlDirectory: htmlDirectory,
      userCount: userCount,
    );
  }

  @override
  String toString() {
    return 'HotspotProfile(name: $name, htmlDirectory: $htmlDirectory, userCount: $userCount)';
  }
}
