# دليل المطور - محرر صفحات Hotspot

## نظرة عامة على البنية

### المجلدات الرئيسية

```
lib/
├── config/          # إعدادات التطبيق
├── models/          # نماذج البيانات
├── screens/         # شاشات التطبيق
├── services/        # خدمات التطبيق
└── utils/           # أدوات مساعدة
```

### الملفات الرئيسية

#### Models (النماذج)
- `hotspot_profile.dart` - نموذج ملف تعريف Hotspot
- `html_content.dart` - نموذج محتوى HTML القابل للتحرير

#### Services (الخدمات)
- `ssh_service.dart` - خدمة الاتصال بـ SSH
- `html_parser_service.dart` - خدمة تحليل وتعديل HTML

#### Screens (الشاشات)
- `connection_screen.dart` - شاشة الاتصال بالخادم
- `profile_selection_screen.dart` - شاشة اختيار ملف التعريف
- `file_list_screen.dart` - شاشة قائمة ملفات HTML
- `html_editor_screen.dart` - شاشة تحرير HTML
- `web_preview_screen.dart` - شاشة معاينة الصفحة

#### Utils (الأدوات)
- `constants.dart` - الثوابت المستخدمة في التطبيق
- `error_handler.dart` - معالج الأخطاء والرسائل

## كيفية عمل التطبيق

### 1. الاتصال بالخادم
```dart
// إنشاء اتصال SSH
final sshService = SSHService();
await sshService.connect(
  host: '***********',
  port: 22,
  username: 'admin',
  password: 'password',
);
```

### 2. البحث عن ملفات التعريف
```dart
// الحصول على جميع ملفات التعريف المتاحة
final profiles = await sshService.getAllProfiles();
```

### 3. تحليل HTML
```dart
// تحليل محتوى HTML
final htmlContent = HtmlParserService.parseHtml(htmlString);
```

### 4. إعادة بناء HTML
```dart
// إعادة بناء HTML بعد التعديل
final modifiedHtml = HtmlParserService.rebuildHtml(
  originalHtml, 
  editedLines
);
```

## إضافة ميزات جديدة

### إضافة شاشة جديدة
1. إنشاء ملف في مجلد `screens/`
2. إضافة التنقل في الشاشة المناسبة
3. تحديث الاختبارات إذا لزم الأمر

### إضافة خدمة جديدة
1. إنشاء ملف في مجلد `services/`
2. تطبيق نمط Singleton إذا لزم الأمر
3. إضافة معالجة الأخطاء المناسبة

### إضافة نموذج بيانات جديد
1. إنشاء ملف في مجلد `models/`
2. إضافة constructors و factory methods
3. إضافة طرق التحويل (toJson/fromJson) إذا لزم الأمر

## اختبار التطبيق

### تشغيل الاختبارات
```bash
flutter test
```

### تشغيل التطبيق
```bash
flutter run
```

### بناء التطبيق للإنتاج
```bash
flutter build apk --release
```

## معالجة الأخطاء

### استخدام ErrorHandler
```dart
try {
  // كود قد يسبب خطأ
} catch (e) {
  ErrorHandler.showError(context, ErrorHandler.getErrorMessage(e));
}
```

### إضافة رسائل خطأ جديدة
1. إضافة الثابت في `constants.dart`
2. استخدام الثابت في الكود
3. إضافة الترجمة إذا لزم الأمر

## أفضل الممارسات

### 1. إدارة الحالة
- استخدام StatefulWidget للشاشات التفاعلية
- تجنب الحالة العامة غير الضرورية

### 2. معالجة الأخطاء
- استخدام try-catch في جميع العمليات غير المتزامنة
- عرض رسائل خطأ واضحة للمستخدم

### 3. الأداء
- تجنب إعادة بناء الواجهة غير الضرورية
- استخدام const constructors عند الإمكان

### 4. الأمان
- عدم تخزين كلمات المرور في النص الواضح
- التحقق من صحة المدخلات

## التطوير المستقبلي

### ميزات مقترحة
1. دعم تحرير CSS
2. إضافة نظام النسخ الاحتياطية
3. دعم تعدد اللغات
4. إضافة محرر WYSIWYG
5. دعم FTP بالإضافة إلى SSH

### تحسينات الأداء
1. إضافة نظام التخزين المؤقت
2. تحسين تحليل HTML
3. ضغط الصور قبل الرفع
4. إضافة التحميل التدريجي

## المساهمة

### قواعد الكود
1. استخدام أسماء واضحة للمتغيرات والدوال
2. إضافة تعليقات للكود المعقد
3. اتباع نمط Dart الرسمي
4. كتابة اختبارات للميزات الجديدة

### عملية المراجعة
1. اختبار الكود محلياً
2. التأكد من عدم كسر الاختبارات الموجودة
3. إضافة وثائق للميزات الجديدة
4. مراجعة الكود مع الفريق
