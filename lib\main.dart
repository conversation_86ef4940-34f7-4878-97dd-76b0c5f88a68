import 'package:flutter/material.dart';
import 'screens/connection_screen.dart';

void main() {
  runApp(const HotspotEditorApp());
}

class HotspotEditorApp extends StatelessWidget {
  const HotspotEditorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'محرر صفحات Hotspot',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        // Support for Arabic text
        fontFamily: 'Arial',
      ),
      home: const ConnectionScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
