class AppConfig {
  // App Information
  static const String appName = 'محرر صفحات Hotspot';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق لتحرير صفحات Hotspot عبر SSH';
  
  // Development Settings
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  
  // Network Settings
  static const int connectionTimeoutSeconds = 30;
  static const int readTimeoutSeconds = 15;
  static const int maxRetryAttempts = 3;
  
  // File Settings
  static const int maxFileSizeBytes = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageExtensions = [
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'
  ];
  
  // UI Settings
  static const double borderRadius = 8.0;
  static const double elevation = 2.0;
  static const int animationDurationMs = 300;
  
  // Text Settings
  static const int maxTextLineLength = 500;
  static const int minTextLineLength = 1;
  
  // Cache Settings
  static const int maxCacheSize = 50; // Number of cached items
  static const int cacheExpirationHours = 24;
  
  // Security Settings
  static const bool requireSecureConnection = true;
  static const int sessionTimeoutMinutes = 30;
  
  // Feature Flags
  static const bool enableImageUpload = true;
  static const bool enablePreview = true;
  static const bool enableAutoSave = false;
  static const bool enableBackup = true;
  
  // Default Values
  static const String defaultHtmlDirectory = '/flash/hotspot';
  static const String defaultTempDirectory = '/tmp';
  static const String defaultBackupDirectory = '/flash/backup';
  
  // Validation Rules
  static bool isValidIpAddress(String ip) {
    final ipRegex = RegExp(
      r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    );
    return ipRegex.hasMatch(ip);
  }
  
  static bool isValidPort(int port) {
    return port >= 1 && port <= 65535;
  }
  
  static bool isValidFileName(String fileName) {
    final fileNameRegex = RegExp(r'^[a-zA-Z0-9._-]+$');
    return fileNameRegex.hasMatch(fileName) && fileName.length <= 255;
  }
  
  static bool isAllowedImageExtension(String fileName) {
    final extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return allowedImageExtensions.contains(extension);
  }
  
  // Helper Methods
  static String getAppInfo() {
    return '$appName v$appVersion';
  }
  
  static Duration getConnectionTimeout() {
    return Duration(seconds: connectionTimeoutSeconds);
  }
  
  static Duration getReadTimeout() {
    return Duration(seconds: readTimeoutSeconds);
  }
  
  static Duration getAnimationDuration() {
    return Duration(milliseconds: animationDurationMs);
  }
}
