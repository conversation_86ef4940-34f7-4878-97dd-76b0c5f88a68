import 'package:flutter/material.dart';
import '../services/ssh_service.dart';
import '../models/hotspot_profile.dart';
import 'html_editor_screen.dart';

class FileListScreen extends StatefulWidget {
  final SSHService sshService;
  final HotspotProfile profile;

  const FileListScreen({
    super.key,
    required this.sshService,
    required this.profile,
  });

  @override
  State<FileListScreen> createState() => _FileListScreenState();
}

class _FileListScreenState extends State<FileListScreen> {
  List<String> _htmlFiles = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadHtmlFiles();
  }

  Future<void> _loadHtmlFiles() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final files =
          await widget.sshService.listHtmlFiles(widget.profile.htmlDirectory);
      setState(() {
        _htmlFiles = files;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل الملفات: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _openHtmlFile(String filePath) async {
    try {
      final content = await widget.sshService.readFile(filePath);
      if (content != null && mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => HtmlEditorScreen(
              sshService: widget.sshService,
              filePath: filePath,
              htmlContent: content,
              profile: widget.profile,
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في قراءة الملف')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في فتح الملف: $e')),
        );
      }
    }
  }

  String _getFileName(String filePath) {
    return filePath.split('/').last;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('ملفات HTML - ${widget.profile.name}'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadHtmlFiles,
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.blue.shade50,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ملف التعريف: ${widget.profile.name}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('مجلد HTML: ${widget.profile.htmlDirectory}'),
              ],
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 64,
                              color: Colors.red.shade300,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage!,
                              style: TextStyle(color: Colors.red.shade700),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadHtmlFiles,
                              child: const Text('إعادة المحاولة'),
                            ),
                          ],
                        ),
                      )
                    : _htmlFiles.isEmpty
                        ? const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.folder_open,
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'لا توجد ملفات HTML في هذا المجلد',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _htmlFiles.length,
                            itemBuilder: (context, index) {
                              final filePath = _htmlFiles[index];
                              final fileName = _getFileName(filePath);

                              return Card(
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 4,
                                ),
                                child: ListTile(
                                  leading: const Icon(
                                    Icons.html,
                                    color: Colors.orange,
                                  ),
                                  title: Text(fileName),
                                  subtitle: Text(
                                    filePath,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  trailing: const Icon(Icons.edit),
                                  onTap: () => _openHtmlFile(filePath),
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
    );
  }
}
