# دليل البدء السريع

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
# تثبيت المكتبات
flutter pub get
```

### 2. تشغيل التطبيق
```bash
# للتطوير
flutter run --debug

# للإنتاج
flutter run --release
```

### 3. بناء التطبيق
```bash
# Android APK
flutter build apk --release

# Windows
flutter build windows --release
```

## الاستخدام السريع

### 1. الاتصال بالخادم
- افتح التطبيق
- أدخل عنوان IP الخاص بجهاز MikroTik
- أدخل اسم المستخدم وكلمة المرور
- اضغط "اتصال"

### 2. اختيار ملف التعريف
- اختر ملف التعريف المطلوب من القائمة
- ستظهر تفاصيل كل ملف تعريف (الاسم، المجلد، عدد المستخدمين)

### 3. تحرير الملفات
- اختر ملف HTML من القائمة
- عدل النصوص المطلوبة
- اضغط "حفظ" لحفظ التغييرات
- اضغط "معاينة" لرؤية النتيجة

### 4. استبدال الصور
- في قسم الصور، اضغط "استبدال"
- اختر صورة جديدة من المعرض
- ستحل الصورة الجديدة محل القديمة

## إعداد MikroTik

### تفعيل SSH
```
/ip ssh set forwarding-enabled=remote
/ip service enable ssh
```

### إنشاء مستخدم
```
/user add name=editor password=password123 group=full
```

### إعداد Hotspot
```
/ip hotspot user-profile add name=default html-directory=flash/hotspot
```

## استكشاف الأخطاء

### مشاكل الاتصال
- تأكد من تفعيل SSH على MikroTik
- تحقق من صحة عنوان IP
- تأكد من صحة اسم المستخدم وكلمة المرور

### مشاكل الملفات
- تأكد من وجود مجلد HTML
- تحقق من صلاحيات المستخدم
- تأكد من وجود ملفات HTML في المجلد

### مشاكل الصور
- تأكد من دعم تنسيق الصورة
- تحقق من حجم الصورة (أقل من 10MB)
- تأكد من صلاحيات الكتابة

## الدعم

للحصول على المساعدة:
1. راجع ملف README.md للتفاصيل الكاملة
2. راجع DEVELOPER_GUIDE.md للمطورين
3. تحقق من CHANGELOG.md للتحديثات
