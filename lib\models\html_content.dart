class HtmlContent {
  final String originalHtml;
  final List<EditableTextLine> editableLines;
  final List<String> imageUrls;

  HtmlContent({
    required this.originalHtml,
    required this.editableLines,
    required this.imageUrls,
  });
}

class EditableTextLine {
  final int lineNumber;
  final String originalText;
  String currentText;
  final String htmlTag;

  EditableTextLine({
    required this.lineNumber,
    required this.originalText,
    required this.currentText,
    required this.htmlTag,
  });

  EditableTextLine copyWith({
    int? lineNumber,
    String? originalText,
    String? currentText,
    String? htmlTag,
  }) {
    return EditableTextLine(
      lineNumber: lineNumber ?? this.lineNumber,
      originalText: originalText ?? this.originalText,
      currentText: currentText ?? this.currentText,
      htmlTag: htmlTag ?? this.htmlTag,
    );
  }
}
