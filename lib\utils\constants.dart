class AppConstants {
  // SSH Connection
  static const int defaultSshPort = 22;
  static const int connectionTimeout = 30; // seconds
  
  // File Operations
  static const List<String> supportedImageFormats = [
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'
  ];
  
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  
  // Text Limits
  static const int minTextLength = 3;
  static const int maxTextLength = 1000;
  
  // Error Messages
  static const String connectionError = 'فشل في الاتصال بالخادم';
  static const String fileReadError = 'فشل في قراءة الملف';
  static const String fileWriteError = 'فشل في كتابة الملف';
  static const String imageUploadError = 'فشل في رفع الصورة';
  static const String noProfilesFound = 'لم يتم العثور على ملفات تعريف صالحة';
  static const String noHtmlFilesFound = 'لم يتم العثور على ملفات HTML';
  
  // Success Messages
  static const String connectionSuccess = 'تم الاتصال بنجاح';
  static const String fileSaveSuccess = 'تم حفظ الملف بنجاح';
  static const String imageUploadSuccess = 'تم رفع الصورة بنجاح';
  
  // MikroTik Commands
  static const String hotspotProfileCommand = '/ip hotspot user-profile print';
  static const String findHtmlCommand = 'find {directory} -name "*.html" -type f';
  static const String catFileCommand = 'cat "{filePath}"';
  
  // Regex Patterns
  static const String ipAddressPattern = 
      r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$';
  
  static const String portPattern = r'^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$';
}

class AppStrings {
  // App Title
  static const String appTitle = 'محرر صفحات Hotspot';
  
  // Connection Screen
  static const String connectionTitle = 'الاتصال بخادم Hotspot';
  static const String serverAddress = 'عنوان الخادم';
  static const String port = 'المنفذ';
  static const String username = 'اسم المستخدم';
  static const String password = 'كلمة المرور';
  static const String connect = 'اتصال';
  static const String connecting = 'جاري الاتصال...';
  
  // File List Screen
  static const String htmlFiles = 'ملفات HTML';
  static const String refresh = 'تحديث';
  static const String profile = 'ملف التعريف';
  static const String htmlDirectory = 'مجلد HTML';
  static const String userCount = 'عدد المستخدمين';
  
  // Editor Screen
  static const String preview = 'معاينة';
  static const String save = 'حفظ';
  static const String textEditing = 'تحرير النصوص';
  static const String images = 'الصور';
  static const String replaceImage = 'استبدال';
  static const String unsavedChanges = 'هناك تغييرات غير محفوظة';
  
  // Preview Screen
  static const String previewTitle = 'معاينة';
  static const String loadingPage = 'جاري تحميل الصفحة...';
  static const String refreshPage = 'تحديث الصفحة';
  
  // Common
  static const String yes = 'نعم';
  static const String no = 'لا';
  static const String ok = 'موافق';
  static const String cancel = 'إلغاء';
  static const String close = 'إغلاق';
  static const String retry = 'إعادة المحاولة';
  static const String loading = 'جاري التحميل...';
  static const String error = 'خطأ';
  static const String success = 'نجح';
  static const String warning = 'تحذير';
}
