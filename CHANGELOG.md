# سجل التغييرات

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

يتبع هذا المشروع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [غير منشور]

### مضاف
- ميزات جديدة قيد التطوير

### تم التغيير
- تحسينات على الميزات الموجودة

### مُصلح
- إصلاحات الأخطاء

## [1.1.0] - 2024-01-XX

### تم التغيير
- **اختيار ملف التعريف**: تم تغيير آلية اختيار ملف التعريف من البحث التلقائي عن الأكثر استخداماً إلى عرض قائمة بجميع ملفات التعريف المتاحة للاختيار من بينها
- **واجهة محسنة**: إضافة شاشة جديدة لاختيار ملف التعريف مع عرض تفاصيل كل ملف تعريف
- **مرونة أكبر**: المستخدم الآن يمكنه اختيار أي ملف تعريف بغض النظر عن عدد المستخدمين

### مضاف
- شاشة اختيار ملف التعريف الجديدة (`ProfileSelectionScreen`)
- عرض تفاصيل كل ملف تعريف (الاسم، مجلد HTML، عدد المستخدمين)
- إمكانية تحديث قائمة ملفات التعريف
- رسائل خطأ محسنة لحالة عدم وجود ملفات تعريف

### مُصلح
- إصلاح مشاكل BuildContext في العمليات غير المتزامنة
- تحسين معالجة الأخطاء في جميع الشاشات

## [1.0.0] - 2024-01-XX

### مضاف
- **الاتصال عبر SSH**: إمكانية الاتصال بخادم MikroTik عبر SSH
- **اختيار ملف التعريف**: عرض جميع ملفات التعريف المتاحة للاختيار من بينها
- **قائمة ملفات HTML**: عرض جميع ملفات HTML الموجودة في مجلد Hotspot
- **محرر النصوص المبسط**: تحرير النصوص بدون الحاجة لمعرفة HTML
- **استبدال الصور**: إمكانية استبدال الصور من معرض الهاتف
- **معاينة مباشرة**: عرض الصفحة في متصفح داخلي
- **حفظ التغييرات**: حفظ التعديلات مباشرة على الخادم
- **واجهة عربية**: دعم كامل للغة العربية
- **معالجة الأخطاء**: نظام شامل لمعالجة وعرض الأخطاء
- **التحقق من صحة المدخلات**: التحقق من صحة عناوين IP والمنافذ

### الميزات التقنية
- **بنية نظيفة**: تنظيم الكود في طبقات منفصلة (Models, Services, Screens, Utils)
- **تحليل HTML**: تحليل ذكي لملفات HTML لاستخراج النصوص القابلة للتحرير
- **إدارة الاتصالات**: إدارة آمنة لاتصالات SSH
- **معالجة الصور**: رفع وتحويل الصور بتنسيق base64
- **ثوابت التطبيق**: تنظيم جميع الثوابت والنصوص في ملفات منفصلة

### المكتبات المستخدمة
- `dartssh2: ^2.9.0` - للاتصال عبر SSH
- `html: ^0.15.4` - لتحليل ملفات HTML
- `webview_flutter: ^4.4.2` - لعرض معاينة الصفحات
- `image_picker: ^1.0.4` - لاختيار الصور من المعرض
- `path: ^1.8.3` - للتعامل مع مسارات الملفات
- `http: ^1.1.0` - للعمليات الشبكية

### متطلبات النظام
- Flutter SDK 3.6.0 أو أحدث
- Android 5.0 (API level 21) أو أحدث
- iOS 11.0 أو أحدث
- Windows 10 أو أحدث

### الأذونات المطلوبة
#### Android
- `INTERNET` - للاتصال بالشبكة
- `ACCESS_NETWORK_STATE` - لفحص حالة الشبكة
- `READ_EXTERNAL_STORAGE` - لقراءة الصور
- `WRITE_EXTERNAL_STORAGE` - لحفظ الملفات
- `CAMERA` - لالتقاط الصور

#### iOS
- `NSCameraUsageDescription` - للوصول للكاميرا
- `NSPhotoLibraryUsageDescription` - للوصول لمعرض الصور

### الملفات الرئيسية
```
lib/
├── config/
│   └── app_config.dart          # إعدادات التطبيق
├── models/
│   ├── hotspot_profile.dart     # نموذج ملف تعريف Hotspot
│   └── html_content.dart        # نموذج محتوى HTML
├── screens/
│   ├── connection_screen.dart   # شاشة الاتصال
│   ├── file_list_screen.dart    # شاشة قائمة الملفات
│   ├── html_editor_screen.dart  # شاشة التحرير
│   └── web_preview_screen.dart  # شاشة المعاينة
├── services/
│   ├── ssh_service.dart         # خدمة SSH
│   └── html_parser_service.dart # خدمة تحليل HTML
├── utils/
│   ├── constants.dart           # الثوابت
│   └── error_handler.dart       # معالج الأخطاء
└── main.dart                    # نقطة البداية
```

### الاختبارات
- اختبارات الوحدة للخدمات الأساسية
- اختبارات الواجهة للشاشات الرئيسية
- اختبار التكامل للتدفق الكامل

### الوثائق
- `README.md` - دليل المستخدم الأساسي
- `DEVELOPER_GUIDE.md` - دليل المطور
- `DEPLOYMENT.md` - دليل النشر والتوزيع
- `CHANGELOG.md` - سجل التغييرات

### المشاكل المعروفة
- قد تحتاج بعض أجهزة MikroTik لإعدادات SSH خاصة
- رفع الصور الكبيرة قد يستغرق وقتاً أطول
- المعاينة قد لا تعمل مع بعض أنماط CSS المعقدة

### خطط المستقبل
- [ ] دعم تحرير CSS
- [ ] نظام النسخ الاحتياطية التلقائية
- [ ] دعم FTP بالإضافة إلى SSH
- [ ] محرر WYSIWYG متقدم
- [ ] دعم تعدد اللغات
- [ ] نظام القوالب الجاهزة
- [ ] إحصائيات الاستخدام
- [ ] تحسين الأداء والذاكرة

---

## تنسيق سجل التغييرات

### الأنواع
- **مضاف** للميزات الجديدة
- **تم التغيير** للتغييرات في الميزات الموجودة
- **مُهمل** للميزات التي ستُزال قريباً
- **مُزال** للميزات المُزالة
- **مُصلح** لإصلاحات الأخطاء
- **أمان** في حالة الثغرات الأمنية
