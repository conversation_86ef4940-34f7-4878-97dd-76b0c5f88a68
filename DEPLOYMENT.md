# دليل النشر والتوزيع

## متطلبات النشر

### البيئة المطلوبة
- Flutter SDK 3.6.0 أو أحدث
- Android SDK (للنشر على Android)
- Xcode (للنشر على iOS)
- Visual Studio (للنشر على Windows)

### الأذونات المطلوبة

#### Android
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.CAMERA" />
```

#### iOS
```xml
<key>NSCameraUsageDescription</key>
<string>يحتاج التطبيق للوصول للكاميرا لاختيار الصور</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>يحتاج التطبيق للوصول لمعرض الصور لاختيار الصور</string>
```

## بناء التطبيق

### Android APK
```bash
# بناء APK للاختبار
flutter build apk --debug

# بناء APK للإنتاج
flutter build apk --release

# بناء App Bundle للنشر على Google Play
flutter build appbundle --release
```

### iOS
```bash
# بناء للمحاكي
flutter build ios --debug --simulator

# بناء للأجهزة الحقيقية
flutter build ios --release
```

### Windows
```bash
# بناء للويندوز
flutter build windows --release
```

## إعداد التوقيع

### Android
1. إنشاء مفتاح التوقيع:
```bash
keytool -genkey -v -keystore ~/hotspot-key.jks -keyalg RSA -keysize 2048 -validity 10000 -alias hotspot
```

2. إنشاء ملف `android/key.properties`:
```properties
storePassword=<password>
keyPassword=<password>
keyAlias=hotspot
storeFile=<path-to-key>/hotspot-key.jks
```

3. تحديث `android/app/build.gradle`:
```gradle
android {
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}
```

## اختبار ما قبل النشر

### اختبارات التطبيق
```bash
# تشغيل جميع الاختبارات
flutter test

# اختبار التكامل
flutter drive --target=test_driver/app.dart
```

### اختبار الأداء
```bash
# تحليل حجم التطبيق
flutter build apk --analyze-size

# اختبار الذاكرة
flutter run --profile
```

## النشر على المتاجر

### Google Play Store
1. إنشاء حساب مطور على Google Play Console
2. رفع App Bundle:
```bash
flutter build appbundle --release
```
3. ملء معلومات التطبيق
4. إضافة لقطات الشاشة والوصف
5. نشر التطبيق

### Apple App Store
1. إنشاء حساب مطور Apple
2. إعداد App Store Connect
3. بناء وأرشفة التطبيق في Xcode
4. رفع التطبيق عبر Xcode أو Application Loader
5. ملء معلومات التطبيق
6. إرسال للمراجعة

## التحديثات

### إصدار تحديث جديد
1. تحديث رقم الإصدار في `pubspec.yaml`:
```yaml
version: 1.0.1+2
```

2. إضافة ملاحظات التحديث
3. بناء الإصدار الجديد
4. اختبار التحديث
5. نشر على المتاجر

### إدارة الإصدارات
- استخدام Git tags للإصدارات
- توثيق التغييرات في CHANGELOG.md
- اختبار التوافق مع الإصدارات السابقة

## مراقبة التطبيق

### تتبع الأخطاء
- إضافة Firebase Crashlytics
- مراقبة تقارير الأخطاء
- إصلاح الأخطاء الحرجة بسرعة

### تحليل الاستخدام
- إضافة Google Analytics
- تتبع استخدام الميزات
- تحسين التطبيق بناءً على البيانات

## النسخ الاحتياطية

### كود المصدر
- استخدام Git للتحكم في الإصدارات
- نسخ احتياطية منتظمة للمستودع
- حفظ مفاتيح التوقيع بأمان

### قواعد البيانات
- نسخ احتياطية لإعدادات المستخدمين
- حفظ ملفات التكوين
- توثيق عملية الاستعادة

## الأمان

### حماية المفاتيح
- عدم تضمين مفاتيح API في الكود
- استخدام متغيرات البيئة
- تشفير البيانات الحساسة

### اختبار الأمان
- فحص الثغرات الأمنية
- اختبار حقن SQL
- التحقق من صحة المدخلات

## الدعم الفني

### قنوات الدعم
- إنشاء نظام تذاكر الدعم
- توفير وثائق المساعدة
- إنشاء قسم الأسئلة الشائعة

### تحديث الوثائق
- تحديث دليل المستخدم
- توثيق الميزات الجديدة
- ترجمة الوثائق للغات مختلفة
