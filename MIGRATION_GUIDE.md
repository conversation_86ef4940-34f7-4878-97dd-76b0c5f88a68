# دليل الترقية - من الإصدار 1.0.0 إلى 1.1.0

## التغييرات الرئيسية

### 1. تغيير آلية اختيار ملف التعريف

#### الإصدار السابق (1.0.0)
- كان التطبيق يبحث تلقائياً عن ملف التعريف الأكثر استخداماً
- لم يكن هناك خيار للمستخدم لاختيار ملف تعريف مختلف
- الانتقال المباشر من شاشة الاتصال إلى قائمة الملفات

#### الإصدار الجديد (1.1.0)
- عرض جميع ملفات التعريف المتاحة في قائمة
- المستخدم يختار ملف التعريف المطلوب بنفسه
- شاشة جديدة مخصصة لاختيار ملف التعريف
- عرض تفاصيل كل ملف تعريف (الاسم، المجلد، عدد المستخدمين)

## التدفق الجديد للتطبيق

### قبل التحديث
```
شاشة الاتصال → قائمة ملفات HTML → محرر HTML → معاينة
```

### بعد التحديث
```
شاشة الاتصال → اختيار ملف التعريف → قائمة ملفات HTML → محرر HTML → معاينة
```

## الميزات الجديدة

### 1. شاشة اختيار ملف التعريف
- عرض جميع ملفات التعريف في قائمة منظمة
- إمكانية تحديث القائمة
- عرض معلومات مفصلة لكل ملف تعريف
- تصميم متجاوب وسهل الاستخدام

### 2. مرونة أكبر في الاختيار
- لا يعتمد الاختيار على عدد المستخدمين
- يمكن الوصول لجميع ملفات التعريف المتاحة
- إمكانية التبديل بين ملفات التعريف المختلفة

### 3. تحسينات في واجهة المستخدم
- رسائل خطأ محسنة
- تحسين تجربة المستخدم
- إضافة رسائل توضيحية

## التغييرات التقنية

### 1. تغييرات في الخدمات
```dart
// قديم
Future<HotspotProfile?> getMostUsedProfile()

// جديد  
Future<List<HotspotProfile>> getAllProfiles()
```

### 2. شاشات جديدة
- إضافة `ProfileSelectionScreen`
- تحديث `ConnectionScreen` للتوجه للشاشة الجديدة

### 3. ثوابت جديدة
- إضافة نصوص جديدة في `AppStrings`
- تحديث رسائل الخطأ والنجاح

## فوائد التحديث

### 1. للمستخدمين
- **مرونة أكبر**: اختيار أي ملف تعريف مطلوب
- **شفافية أكثر**: رؤية جميع الخيارات المتاحة
- **سهولة الاستخدام**: واجهة واضحة ومنظمة

### 2. للمطورين
- **كود أنظف**: فصل منطق اختيار ملف التعريف
- **قابلية التوسع**: سهولة إضافة ميزات جديدة
- **صيانة أسهل**: كود منظم ومقسم بشكل أفضل

## التوافق مع الإصدارات السابقة

### البيانات
- لا توجد تغييرات في تنسيق البيانات
- جميع ملفات التعريف الموجودة ستعمل بشكل طبيعي

### الإعدادات
- لا حاجة لإعادة إعداد الخادم
- جميع إعدادات SSH تبقى كما هي

### الملفات
- لا تغيير في بنية ملفات HTML
- جميع الملفات الموجودة ستعمل بشكل طبيعي

## خطوات الترقية

### 1. للمستخدمين
```bash
# تحديث التطبيق
flutter pub get
flutter run
```

### 2. للمطورين
```bash
# تحديث المكتبات
flutter pub get

# تشغيل الاختبارات
flutter test

# تحليل الكود
flutter analyze
```

## استكشاف الأخطاء

### مشكلة: لا تظهر ملفات التعريف
**الحل:**
- تأكد من وجود ملفات تعريف على الخادم
- تحقق من صلاحيات المستخدم
- تأكد من إعداد `html-directory` في ملفات التعريف

### مشكلة: خطأ في الاتصال
**الحل:**
- تأكد من صحة بيانات الاتصال
- تحقق من تفعيل SSH على MikroTik
- تأكد من إعدادات الشبكة

## الدعم

للحصول على المساعدة:
1. راجع ملف README.md المحدث
2. راجع DEVELOPER_GUIDE.md للتفاصيل التقنية
3. تحقق من CHANGELOG.md للتغييرات الكاملة

## خطط المستقبل

### الإصدار القادم (1.2.0)
- [ ] إضافة إمكانية البحث في ملفات التعريف
- [ ] حفظ ملف التعريف المفضل
- [ ] إضافة إحصائيات لكل ملف تعريف
- [ ] تحسين أداء تحميل القوائم

### الإصدارات المستقبلية
- [ ] دعم تحرير ملفات CSS
- [ ] نظام النسخ الاحتياطية
- [ ] دعم FTP بالإضافة إلى SSH
- [ ] محرر WYSIWYG متقدم
