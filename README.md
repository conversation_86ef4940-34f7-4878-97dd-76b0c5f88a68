# محرر صفحات Hotspot

تطبيق Flutter لتحرير ملفات HTML الخاصة بصفحات Hotspot عبر SSH.

## الميزات

- **الاتصال عبر SSH**: اتصال آمن بخادم MikroTik
- **البحث التلقائي**: يجد تلقائياً ملف التعريف الأكثر استخداماً في Hotspot
- **تحرير مبسط**: واجهة سهلة لتحرير النصوص بدون معرفة HTML
- **استبدال الصور**: إمكانية استبدال الصور من معرض الهاتف
- **معاينة مباشرة**: عرض الصفحة في متصفح داخلي
- **حفظ تلقائي**: حفظ التغييرات مباشرة على الخادم

## كيفية الاستخدام

### 1. الاتصال بالخادم
- أدخل عنوان IP الخاص بجهاز MikroTik
- أدخل رقم المنفذ (افتراضي: 22)
- أدخل اسم المستخدم وكلمة المرور
- اضغط "اتصال"

### 2. اختيار ملف HTML
- سيظهر لك قائمة بجميع ملفات HTML الموجودة
- اختر الملف الذي تريد تحريره

### 3. تحرير المحتوى
- ستظهر لك قائمة بجميع النصوص القابلة للتحرير
- قم بتعديل أي نص تريده
- لا تحتاج لمعرفة HTML - فقط عدل النص

### 4. استبدال الصور
- في قسم الصور، اضغط "استبدال" بجانب أي صورة
- اختر صورة جديدة من معرض الهاتف
- ستحل الصورة الجديدة محل القديمة بنفس الاسم

### 5. حفظ ومعاينة
- اضغط أيقونة الحفظ لحفظ التغييرات
- اضغط أيقونة المعاينة لرؤية النتيجة النهائية

## المتطلبات التقنية

- Flutter SDK
- جهاز MikroTik مع تفعيل SSH
- صلاحيات قراءة وكتابة ملفات Hotspot

## التثبيت والتشغيل

```bash
# تثبيت المكتبات
flutter pub get

# تشغيل التطبيق
flutter run
```

## ملاحظات مهمة

- تأكد من أن SSH مفعل على جهاز MikroTik
- تأكد من وجود صلاحيات كافية للمستخدم
- يتم البحث عن ملف التعريف الأكثر استخداماً تلقائياً
- يتم حفظ النسخ الاحتياطية تلقائياً قبل التعديل

## الدعم الفني

في حالة وجود مشاكل:
1. تأكد من صحة بيانات الاتصال
2. تأكد من تفعيل SSH على MikroTik
3. تأكد من وجود ملفات HTML في المجلد المحدد
