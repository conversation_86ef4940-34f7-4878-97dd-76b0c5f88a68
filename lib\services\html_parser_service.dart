import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;
import '../models/html_content.dart';

class HtmlParserService {
  static HtmlContent parseHtml(String htmlContent) {
    final document = html_parser.parse(htmlContent);
    final editableLines = <EditableTextLine>[];
    final imageUrls = <String>[];

    int lineNumber = 1;

    // Extract text content from various HTML elements
    _extractTextFromElement(
        document.body, editableLines, lineNumber, imageUrls);

    return HtmlContent(
      originalHtml: htmlContent,
      editableLines: editableLines,
      imageUrls: imageUrls,
    );
  }

  static void _extractTextFromElement(
    dom.Element? element,
    List<EditableTextLine> editableLines,
    int lineNumber,
    List<String> imageUrls,
  ) {
    if (element == null) return;

    // Extract images
    final images = element.querySelectorAll('img');
    for (var img in images) {
      final src = img.attributes['src'];
      if (src != null && src.isNotEmpty) {
        imageUrls.add(src);
      }
    }

    // Extract text from common elements, prioritizing meaningful content
    final textElements = element.querySelectorAll(
        'h1, h2, h3, h4, h5, h6, p, div, span, a, li, td, th, label, button, title');

    // Create a set to track processed text to avoid duplicates
    final processedTexts = <String>{};

    for (var textElement in textElements) {
      final text = textElement.text.trim();

      // Skip empty, very short, or already processed text
      if (text.isEmpty || text.length < 3 || processedTexts.contains(text)) {
        continue;
      }

      // Skip if this text is contained within another element's text
      bool isChildText = false;
      for (var existingLine in editableLines) {
        if (existingLine.currentText.contains(text) &&
            existingLine.currentText != text &&
            existingLine.currentText.length > text.length) {
          isChildText = true;
          break;
        }
      }

      // Skip common non-content text
      if (_isNonContentText(text)) {
        continue;
      }

      if (!isChildText) {
        editableLines.add(EditableTextLine(
          lineNumber: editableLines.length + 1,
          originalText: text,
          currentText: text,
          htmlTag: textElement.localName ?? 'div',
        ));
        processedTexts.add(text);
      }
    }
  }

  static bool _isNonContentText(String text) {
    // Skip common non-content text patterns
    final nonContentPatterns = [
      RegExp(r'^\s*$'), // Only whitespace
      RegExp(r'^[\d\s\-\+\(\)]+$'), // Only numbers and basic punctuation
      RegExp(
          r'^[^\w\u0600-\u06FF]+$'), // Only special characters (no Arabic or Latin letters)
    ];

    for (var pattern in nonContentPatterns) {
      if (pattern.hasMatch(text)) {
        return true;
      }
    }

    return false;
  }

  static String rebuildHtml(
      String originalHtml, List<EditableTextLine> editedLines) {
    String modifiedHtml = originalHtml;

    // Replace text content while preserving HTML structure
    for (var line in editedLines) {
      if (line.originalText != line.currentText) {
        // Use regex to find and replace the exact text content
        final pattern = RegExp(
          '(>\\s*)${RegExp.escape(line.originalText)}(\\s*<)',
          multiLine: true,
        );

        modifiedHtml = modifiedHtml.replaceAllMapped(pattern, (match) {
          return '${match.group(1)}${line.currentText}${match.group(2)}';
        });

        // Also handle cases where text is not between tags
        modifiedHtml =
            modifiedHtml.replaceAll(line.originalText, line.currentText);
      }
    }

    return modifiedHtml;
  }

  static List<String> extractImagePaths(String htmlContent) {
    final document = html_parser.parse(htmlContent);
    final images = document.querySelectorAll('img');
    final imagePaths = <String>[];

    for (var img in images) {
      final src = img.attributes['src'];
      if (src != null && src.isNotEmpty) {
        imagePaths.add(src);
      }
    }

    return imagePaths;
  }

  static String replaceImagePath(
      String htmlContent, String oldPath, String newPath) {
    return htmlContent.replaceAll('src="$oldPath"', 'src="$newPath"');
  }
}
